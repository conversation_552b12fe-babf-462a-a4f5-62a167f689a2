import { apiClient } from "@/config/api";
import { createContext, useContext, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null); // store user data
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Check token and fetch user on app load
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const res = await apiClient.get("/auth/me"); // or your auth user route
        setUser(res.data.user);
      } catch (err) {
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, []);

  const login = async (formData) => {
    const res = await apiClient.post("/auth/login", formData);
    setUser(res.data.user);
    return res;
  };

  const register = async (formData) => {
    const res = await apiClient.post("/auth/register", formData);
    return res;
  };

  const logout = async () => {
    await apiClient.post("/auth/logout");
    setUser(null);
    navigate("/login");
  };

  return (
    <AuthContext.Provider value={{ user, login, register, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook
export const useAuth = () => {
  return useContext(AuthContext);
};
