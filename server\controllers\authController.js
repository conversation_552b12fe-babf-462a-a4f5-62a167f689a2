const bcrypt = require("bcrypt");
const crypto = require("crypto");
const User = require("../models/User");
const { sendEmail } = require("../utils/mailer");
const jwt = require("jsonwebtoken");

// Middleware to verify JWT token
const verifyToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: "Access denied. No token provided."
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Invalid token. User not found."
      });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error("Token verification error:", error);
    return res.status(401).json({
      success: false,
      message: "Invalid token."
    });
  }
};

exports.registerUser = async (req, res) => {
  try {
    const { username, email, password } = req.body;

    if (!username || !email || !password) {
      return res
        .status(400)
        .json({ success: false, message: "All fields are required" });
    }

    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res
        .status(409)
        .json({ success: false, message: "Email or username already exists" });
    }

    const hashedPassword = await bcrypt.hash(password, 12);

    const emailVerifyToken = crypto.randomBytes(32).toString("hex");
    const emailVerifyExpires = Date.now() + 60 * 60 * 1000; // 1 hour

    const newUser = new User({
      username,
      email,
      password: hashedPassword,
      emailVerifyToken,
      emailVerifyExpires,
    });

    await newUser.save();

    // ✅ Construct email verification URL
    const verifyUrl = `${process.env.BASE_URL}/verify-email/${newUser._id}/${emailVerifyToken}`;

    // ✅ Email content
    const html = `
      <h3>Welcome to GigGlobe!</h3>
      <p>Click the link below to verify your email:</p>
      <a href="${verifyUrl}" target="_blank">Verify Email</a>
      <p>This link will expire in 1 hour.</p>
    `;

    await sendEmail(email, "Verify Your Email - GigGlobe", html);

    return res.status(201).json({
      success: true,
      message:
        "User registered successfully. A verification email has been sent.",
    });
  } catch (err) {
    console.error("Register error:", err);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

exports.resendVerificationEmail = async (req, res) => {
  const { email, userId } = req.body;

  if (!email && !userId) {
    return res
      .status(400)
      .json({ success: false, message: "Email or userId is required" });
  }

  try {
    // Find user by email or userId
    const user = email
      ? await User.findOne({ email })
      : await User.findById(userId);

    if (!user) {
      return res
        .status(404)
        .json({ success: false, message: "User not found" });
    }

    if (user.isVerified) {
      return res
        .status(400)
        .json({ success: false, message: "Account already verified" });
    }

    // Generate new token and expiry
    const token = crypto.randomBytes(32).toString("hex");
    const expiry = Date.now() + 60 * 60 * 1000; // 1 hour

    user.emailVerifyToken = token;
    user.emailVerifyExpires = expiry;
    await user.save();

    const verifyUrl = `${process.env.BASE_URL}/verify-email/${user._id}/${token}`;

    const html = `
      <h3>Welcome to GigGlobe!</h3>
      <p>Click the link below to verify your email:</p>
      <a href="${verifyUrl}" target="_blank">Verify Email</a>
      <p>This link will expire in 1 hour.</p>
    `;

    await sendEmail(user.email, "Verify Your Email - GigGlobe", html);

    return res.status(200).json({
      success: true,
      message: "Verification email resent successfully",
    });
  } catch (err) {
    console.error("Resend verification error:", err);
    return res.status(500).json({
      success: false,
      message: "Server error, please try again later",
    });
  }
};

exports.verifyEmail = async (req, res) => {
  const { userId, token } = req.params;

  // Validate input parameters
  if (!userId || !token) {
    return res
      .status(400)
      .json({ success: false, message: "Invalid verification link." });
  }

  try {
    const user = await User.findById(userId);

    if (!user) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired verification link.",
      });
    }

    // Check if user is already verified
    if (user.isVerified) {
      return res
        .status(200)
        .json({ success: true, message: "Email is already verified!" });
    }

    // Token check
    if (
      user.emailVerifyToken !== token ||
      !user.emailVerifyExpires ||
      user.emailVerifyExpires < Date.now()
    ) {
      return res.status(400).json({
        success: false,
        message: "Verification link is invalid or has expired.",
      });
    }

    // Update user
    user.isVerified = true;
    user.emailVerifyToken = undefined;
    user.emailVerifyExpires = undefined;
    await user.save();

    return res
      .status(200)
      .json({ success: true, message: "Email verified successfully!" });
  } catch (err) {
    console.error("Email verification error:", err);
    res.status(500).json({ success: false, message: "Server error" });
  }
};

exports.loginUser = async (req, res) => {
  const { email, password } = req.body;

  // Input validation
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: "Email and password are required",
    });
  }

  try {
    // Find user and explicitly select the password field
    const user = await User.findOne({ email }).select("+password");

    // User not found
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "Invalid email or password",
      });
    }

    // Check if email is verified
    if (!user.isVerified) {
      return res.status(401).json({
        success: false,
        message: "Please verify your email before logging in",
      });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: "Invalid email or password",
      });
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate JWT
    const token = jwt.sign(
      { userId: user._id, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: "7d" }
    );

    // Set token in header
    res.setHeader("Authorization", `Bearer ${token}`);

    // Return success (also include token in response for client-side storage)
    res.status(200).json({
      success: true,
      message: "Login successful",
      token,
      user: {
        id: user._id,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        role: user.role,
        isSeller: user.isSeller,
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ success: false, message: "Server error" });
  }
};

exports.forgotPassword = async (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      success: false,
      message: "Email is required",
    });
  }

  try {
    const user = await User.findOne({ email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User with this email does not exist",
      });
    }

    // Generate reset token
    const token = crypto.randomBytes(32).toString("hex");
    const expiry = Date.now() + 60 * 60 * 1000; // 1 hour

    user.passwordResetToken = token;
    user.passwordResetExpires = expiry;
    await user.save();

    const resetUrl = `${process.env.BASE_URL}/reset-password/${user._id}/${token}`;

    const html = `
      <h2>Reset Your Password</h2>
      <p>Click the link below to reset your password:</p>
      <a href="${resetUrl}" target="_blank">${resetUrl}</a>
      <p>This link will expire in 1 hour.</p>
    `;

    await sendEmail(email, "Reset Your Password - GigGlobe", html);

    res.status(200).json({
      success: true,
      message: "Password reset link sent to your email",
    });
  } catch (err) {
    console.error("Forgot password error:", err);
    res.status(500).json({ success: false, message: "Server error" });
  }
};

exports.resetPassword = async (req, res) => {
  const { userId, token } = req.params;
  const { password } = req.body;

  if (!password) {
    return res.status(400).json({
      success: false,
      message: "Password is required",
    });
  }

  try {
    const user = await User.findById(userId);

    if (
      !user ||
      user.passwordResetToken !== token ||
      user.passwordResetExpires < Date.now()
    ) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired password reset token",
      });
    }

    // Hash and set new password
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(password, salt);

    // Clear reset fields
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;

    await user.save();

    res.status(200).json({
      success: true,
      message: "Password has been reset successfully",
    });
  } catch (err) {
    console.error("Reset password error:", err);
    res.status(500).json({ success: false, message: "Server error" });
  }
};

exports.logoutUser = async (_req, res) => {
  try {
    // Since we're using JWT tokens (stateless), we don't need to destroy sessions
    // The client will remove the token from localStorage
    // In a production app, you might want to maintain a blacklist of invalidated tokens

    res.status(200).json({
      success: true,
      message: "Logout successful",
    });
  } catch (error) {
    console.error("Logout error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to logout",
    });
  }
};

// Get current user profile
exports.getCurrentUser = async (req, res) => {
  try {
    const user = req.user; // Set by verifyToken middleware

    res.status(200).json({
      success: true,
      user: {
        id: user._id,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        role: user.role,
        isSeller: user.isSeller,
        isVerified: user.isVerified,
      },
    });
  } catch (error) {
    console.error("Get current user error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get user profile",
    });
  }
};

// Export the middleware for use in routes
exports.verifyToken = verifyToken;
