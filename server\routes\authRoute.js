const express = require("express");
const router = express.Router();
const {
  registerUser,
  resendVerificationEmail,
  verifyEmail,
  loginUser,
  forgotPassword,
  resetPassword,
  logoutUser,
  getCurrentUser,
  verifyToken,
} = require("../controllers/authController");

router.post("/register", registerUser);
router.post("/resend-verification", resendVerificationEmail);
router.get("/verify-email/:userId/:token", verifyEmail);
router.post("/login", loginUser);
router.post("/forgot-password", forgotPassword);
router.post("/reset-password/:userId/:token", resetPassword);
router.post("/logout", logoutUser);
router.get("/me", verifyToken, getCurrentUser);

module.exports = router;
